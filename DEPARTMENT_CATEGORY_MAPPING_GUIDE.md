# Department-Category Mapping System Guide

## Overview

The department-category mapping system has been enhanced to support **flexible mapping relationships** that allow both:

1. **Multiple departments → Same category** (e.g., Food, Wood Fire Pizza → FOOD)
2. **Single department → Multiple categories** (e.g., Kitchen → FOOD, BEVERAGES, DESSERTS)

This flexibility enables better reconciliation and reporting for complex restaurant operations.

## Key Features

### 1. Flexible Mapping Support
- **Multiple departments can map to the same category**: Perfect for scenarios where different POS departments represent the same inventory category
- **Single department can map to multiple categories**: Allows comprehensive department coverage
- **No artificial restrictions**: The system validates only for data integrity, not business logic constraints

### 2. Enhanced Reconciliation
- **Category-wise aggregation**: Data from multiple departments mapping to the same category is properly aggregated
- **Department-wise reporting**: Sales and consumption can be viewed by department while maintaining category relationships
- **Cross-category indent calculations**: Supports complex inventory movements between categories

### 3. Improved User Interface
- **Clear guidance**: Interface shows that multiple departments can map to the same category
- **Visual feedback**: Users can see mapping relationships clearly
- **Validation messages**: Only shows errors for actual data integrity issues

## Usage Examples

### Example 1: Multiple Departments → Same Category

**Scenario**: You have separate POS departments for "Food" and "Wood Fire Pizza" but they both use the same inventory category "FOOD".

**Configuration**:
```
Department: Food (ID: 101)
├── Categories: [FOOD]

Department: Wood Fire Pizza (ID: 102)  
├── Categories: [FOOD]
```

**Result**: Both departments' sales will be tracked separately, but inventory consumption will be aggregated under the FOOD category for reconciliation.

### Example 2: Single Department → Multiple Categories

**Scenario**: Your "Kitchen" department handles multiple types of inventory.

**Configuration**:
```
Department: Kitchen (ID: 201)
├── Categories: [FOOD, BEVERAGES, DESSERTS]
```

**Result**: Kitchen sales will be distributed across multiple categories based on actual consumption patterns.

### Example 3: Complex Mixed Mapping

**Configuration**:
```
Department: Food (ID: 101)
├── Categories: [FOOD, DESSERTS]

Department: Wood Fire Pizza (ID: 102)
├── Categories: [FOOD]

Department: Bar (ID: 103)
├── Categories: [BEVERAGES, DESSERTS]
```

**Result**: 
- FOOD category: Receives data from Food and Wood Fire Pizza departments
- DESSERTS category: Receives data from Food and Bar departments  
- BEVERAGES category: Receives data only from Bar department

## Technical Implementation

### Backend Changes

1. **Validation Logic Updated** (`department.service.ts`):
   - Removed restriction preventing multiple departments from mapping to same category
   - Added validation for data integrity (no duplicate departments, no empty categories)
   - Added new methods for reverse lookups (category → departments)

2. **Reconciliation Logic Enhanced** (`dashboard_agents.py`):
   - Updated to handle multiple departments per category
   - Enhanced food cost analysis to properly aggregate data
   - Improved department-wise reporting with category relationships

### Frontend Changes

1. **User Interface Improvements**:
   - Added informational message explaining flexible mapping
   - Enhanced visual styling for better user guidance
   - Maintained existing functionality while removing artificial restrictions

2. **Component Updates**:
   - Updated validation to use new service methods
   - Enhanced form handling for complex mapping scenarios
   - Improved error messaging for actual issues

## Configuration Steps

### Step 1: Access Department Mapping
1. Navigate to Smart Dashboard → Reconciliation
2. Click "Configure Department & Category Mapping"

### Step 2: Select Departments
1. Choose all departments you want to configure
2. Multiple departments can be selected simultaneously

### Step 3: Assign Categories
1. For each department, select appropriate categories
2. **Multiple departments can select the same category**
3. **Single department can select multiple categories**
4. The system will show a helpful message explaining this flexibility

### Step 4: Configure Work Areas (Optional)
1. Assign work areas to categories as needed
2. Work area mappings remain exclusive (one work area per category)

### Step 5: Save Configuration
1. Click "Save and Close"
2. System validates for data integrity only
3. Configuration is applied to reconciliation calculations

## Reconciliation Impact

### Data Aggregation
- **Category Level**: Data from multiple departments is aggregated at category level
- **Department Level**: Individual department performance can still be tracked
- **Work Area Level**: Maintains granular tracking for operational insights

### Reporting Benefits
- **Comprehensive Coverage**: No gaps in department-category relationships
- **Flexible Analysis**: View data by department or by category as needed
- **Accurate Reconciliation**: Proper inventory accounting across complex structures

## Best Practices

### 1. Logical Grouping
- Map departments to categories based on actual inventory usage
- Consider how your POS system categorizes sales vs. how inventory is managed

### 2. Consistent Naming
- Use clear, consistent category names across all departments
- Avoid creating duplicate categories with slight name variations

### 3. Regular Review
- Periodically review mappings to ensure they reflect current operations
- Update mappings when adding new departments or categories

### 4. Testing
- Test reconciliation reports after configuration changes
- Verify that data aggregation matches expectations

## Troubleshooting

### Common Issues

1. **Missing Data in Reconciliation**
   - Verify department-category mappings are saved
   - Check that categories exist in inventory data
   - Ensure work area mappings are configured if needed

2. **Duplicate Data**
   - Review for accidentally duplicated department entries
   - Check for case-sensitive category name variations

3. **Validation Errors**
   - Only data integrity issues will show errors
   - Multiple departments mapping to same category is allowed and expected

### Support
For technical issues or questions about the mapping system, contact the development team with:
- Current mapping configuration
- Expected vs. actual reconciliation results
- Any error messages received
