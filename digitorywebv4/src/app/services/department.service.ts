import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface Department {
  id: string;
  name: string;
  code?: string;
  description?: string;
  isActive?: boolean;
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
}

export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
  virtualWorkAreas?: any[];
}

export interface MappingConfig {
  departmentCategoryMappings: DepartmentCategoryMapping[];
  categoryWorkareaMappings: CategoryWorkareaMapping[];
  lastUpdated?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly engineUrl = environment.engineUrl;

  constructor(private readonly http: HttpClient) {}

  // ===== DEPARTMENT METHODS =====
  /**
   * Get all departments for a tenant (via secure backend endpoint)
   */
  getDepartments(tenantId: string): Observable<Department[]> {
    return this.http.get<any>(`${this.engineUrl}api/smart-dashboard/departments/${tenantId}`)
      .pipe(
        map(response => {
          if (response.status === 'success' && response.data) {
            const departments = response.data.map((dept: any) => ({
              id: String(dept.id),
              name: dept.name,
              code: dept.code,
              description: dept.description,
              isActive: dept.isActive !== false
            }));
            return departments;
          }
          throw new Error(response.message || 'Failed to fetch departments');
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching departments:', error);
          return throwError(() => new Error('Failed to fetch departments'));
        })
      );
  }

  // ===== DEPARTMENT-CATEGORY MAPPING METHODS =====
  /**
   * Get department-category mappings for a tenant from database
   */
  getDepartmentCategoryMappings(tenantId: string): Observable<DepartmentCategoryMapping[]> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.departmentCategoryMappings || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching department-category mappings:', error);
          return throwError(() => new Error('Failed to fetch department-category mappings'));
        })
      );
  }

  /**
   * Save department-category mappings to database
   */
  saveDepartmentCategoryMappings(tenantId: string, mappings: DepartmentCategoryMapping[]): Observable<boolean> {
    console.log('DepartmentService: saveDepartmentCategoryMappings called with:', {
      tenantId,
      mappings,
      mappingsLength: mappings.length
    });

    const requestBody = {
      tenantId: tenantId,
      departmentCategoryMappings: mappings,
      categoryWorkareaMappings: [] // Will be updated separately
    };

    console.log('DepartmentService: Request body being sent:', requestBody);

    return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error saving department-category mappings:', error);
          return throwError(() => new Error('Failed to save department-category mappings'));
        })
      );
  }

  /**
   * Get categories mapped to a specific department
   */
  getCategoriesForDepartment(tenantId: string, departmentId: string): Observable<string[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.departmentId === departmentId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Get department for a specific category
   */
  getDepartmentForCategory(tenantId: string, category: string): Observable<string | null> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categories.includes(category));
        return mapping ? mapping.departmentId : null;
      })
    );
  }

  // ===== COMPLETE MAPPING CONFIGURATION METHODS =====
  /**
   * Get complete mapping configuration (department-category + category-workarea)
   */
  getMappingConfig(tenantId: string): Observable<MappingConfig> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return {
              departmentCategoryMappings: response.data.departmentCategoryMappings || [],
              categoryWorkareaMappings: response.data.categoryWorkareaMappings || [],
              lastUpdated: response.data.lastUpdated
            };
          }
          return {
            departmentCategoryMappings: [],
            categoryWorkareaMappings: [],
            lastUpdated: undefined
          };
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching mapping config:', error);
          return throwError(() => new Error('Failed to fetch mapping configuration'));
        })
      );
  }

  /**
   * Save complete mapping configuration (department-category + category-workarea)
   */
  saveMappingConfig(tenantId: string, config: MappingConfig): Observable<boolean> {
    const requestBody = {
      tenantId: tenantId,
      departmentCategoryMappings: config.departmentCategoryMappings,
      categoryWorkareaMappings: config.categoryWorkareaMappings
    };

    console.log('DepartmentService: Sending request to API:', requestBody);

    return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
      .pipe(
        map(response => {
          console.log('DepartmentService: API response:', response);
          return response.success || false;
        }),
        catchError(error => {
          console.error('DepartmentService: Error saving mapping config:', error);
          return throwError(() => new Error('Failed to save mapping configuration'));
        })
      );
  }

  /**
   * Clear mapping configuration for a tenant
   */
  clearMappingConfig(tenantId: string): Observable<boolean> {
    return this.http.delete<any>(`${this.engineUrl}master_data/clear-mapping-config/${tenantId}`)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error clearing mapping config:', error);
          return throwError(() => new Error('Failed to clear mapping configuration'));
        })
      );
  }

  // ===== CATEGORY-WORKAREA MAPPING METHODS =====
  /**
   * Get category-workarea mappings for a tenant
   */
  getCategoryWorkareaMappings(tenantId: string): Observable<CategoryWorkareaMapping[]> {
    return this.getMappingConfig(tenantId).pipe(
      map(config => config.categoryWorkareaMappings)
    );
  }

  /**
   * Save category-workarea mappings (preserves department-category mappings)
   */
  saveCategoryWorkareaMappings(tenantId: string, mappings: CategoryWorkareaMapping[]): Observable<boolean> {
    console.log('DepartmentService: Saving category-workarea mappings for tenant:', tenantId);
    console.log('DepartmentService: Mappings to save:', mappings);

    return this.getMappingConfig(tenantId).pipe(
      switchMap((currentConfig: MappingConfig) => {
        console.log('DepartmentService: Current config:', currentConfig);

        const updatedConfig: MappingConfig = {
          departmentCategoryMappings: currentConfig.departmentCategoryMappings,
          categoryWorkareaMappings: mappings
        };

        console.log('DepartmentService: Updated config to save:', updatedConfig);
        return this.saveMappingConfig(tenantId, updatedConfig);
      })
    );
  }

  /**
   * Get workareas mapped to a specific category
   */
  getWorkareasForCategory(tenantId: string, categoryName: string): Observable<string[]> {
    return this.getCategoryWorkareaMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categoryName === categoryName);
        return mapping ? mapping.workAreas : [];
      })
    );
  }

  /**
   * Get category for a specific workarea
   */
  getCategoryForWorkarea(tenantId: string, workarea: string): Observable<string | null> {
    return this.getCategoryWorkareaMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.workAreas.includes(workarea));
        return mapping ? mapping.categoryName : null;
      })
    );
  }

  // ===== UTILITY METHODS =====

  /**
   * Validate department-category mapping rules
   * - Single department can have multiple categories
   * - Multiple departments can be mapped to the same category (flexible mapping)
   * - Validates for basic data integrity only
   */
  validateMappings(mappings: DepartmentCategoryMapping[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const departmentIds = new Set<string>();

    for (const mapping of mappings) {
      // Check for duplicate department IDs
      if (departmentIds.has(mapping.departmentId)) {
        errors.push(`Duplicate department mapping found for department: "${mapping.departmentName}"`);
      } else {
        departmentIds.add(mapping.departmentId);
      }

      // Check for empty categories
      if (!mapping.categories || mapping.categories.length === 0) {
        errors.push(`Department "${mapping.departmentName}" must have at least one category assigned`);
      }

      // Check for duplicate categories within the same department
      const categorySet = new Set<string>();
      for (const category of mapping.categories || []) {
        if (categorySet.has(category)) {
          errors.push(`Department "${mapping.departmentName}" has duplicate category: "${category}"`);
        } else {
          categorySet.add(category);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get all departments mapped to a specific category
   * Supports multiple departments per category
   */
  getDepartmentsForCategory(tenantId: string, categoryName: string): Observable<Department[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      switchMap(mappings => {
        const departmentIds = mappings
          .filter(m => m.categories.includes(categoryName))
          .map(m => m.departmentId);

        return this.getDepartments(tenantId).pipe(
          map(departments => departments.filter(d => departmentIds.includes(d.id)))
        );
      })
    );
  }

  /**
   * Get category-department mapping summary for reconciliation
   * Returns a map of categories to their associated departments
   */
  getCategoryDepartmentSummary(tenantId: string): Observable<Map<string, Department[]>> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      switchMap(mappings => {
        return this.getDepartments(tenantId).pipe(
          map(departments => {
            const categoryDepartmentMap = new Map<string, Department[]>();

            mappings.forEach(mapping => {
              const department = departments.find(d => d.id === mapping.departmentId);
              if (department) {
                mapping.categories.forEach(category => {
                  if (!categoryDepartmentMap.has(category)) {
                    categoryDepartmentMap.set(category, []);
                  }
                  categoryDepartmentMap.get(category)!.push(department);
                });
              }
            });

            return categoryDepartmentMap;
          })
        );
      })
    );
  }
}
