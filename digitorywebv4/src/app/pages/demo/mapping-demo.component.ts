import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { UnifiedMappingTableComponent, DepartmentCategoryMapping, CategoryWorkareaMapping } from '../../components/unified-mapping-table/unified-mapping-table.component';

@Component({
  selector: 'app-mapping-demo',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatDividerModule,
    MatSnackBarModule,
    UnifiedMappingTableComponent
  ],
  template: `
    <div class="demo-container">
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-icon mat-card-avatar>tune</mat-icon>
          <mat-card-title>Unified Department Mapping Demo</mat-card-title>
          <mat-card-subtitle>
            Configure department → category → workarea mappings in a single view
          </mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <p class="demo-description">
            This demo shows the new unified mapping interface that simplifies the configuration process.
            Users can now see the complete mapping chain in one view, making it easier to understand and configure.
          </p>
          
          <mat-divider></mat-divider>
          
          <div class="mapping-container">
            <app-unified-mapping-table
              [tenantId]="tenantId"
              [autoEmit]="true"
              (mappingsChanged)="onMappingsChanged($event)"
              (categoryWorkareaMappingsChanged)="onCategoryWorkareaMappingsChanged($event)">
            </app-unified-mapping-table>
          </div>
        </mat-card-content>
      </mat-card>
      
      <mat-card class="results-card" *ngIf="showResults">
        <mat-card-header>
          <mat-icon mat-card-avatar>data_object</mat-icon>
          <mat-card-title>Current Configuration</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <h3>Department → Category Mappings</h3>
          <pre>{{ departmentMappings | json }}</pre>
          
          <h3>Category → Workarea Mappings</h3>
          <pre>{{ categoryMappings | json }}</pre>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .demo-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .demo-card {
      margin-bottom: 24px;
    }
    
    .demo-description {
      margin: 16px 0;
      color: #666;
      line-height: 1.5;
    }
    
    mat-divider {
      margin: 16px 0;
    }
    
    .mapping-container {
      margin-top: 24px;
    }
    
    .results-card {
      background: #f8f9fa;
      
      pre {
        background: #fff;
        padding: 16px;
        border-radius: 4px;
        overflow: auto;
        max-height: 300px;
        border: 1px solid #e0e0e0;
      }
    }
  `]
})
export class MappingDemoComponent {
  tenantId: string = 'demo-tenant-123';
  departmentMappings: DepartmentCategoryMapping[] = [];
  categoryMappings: CategoryWorkareaMapping[] = [];
  showResults: boolean = false;
  
  constructor(private snackBar: MatSnackBar) {}
  
  onMappingsChanged(mappings: DepartmentCategoryMapping[]): void {
    this.departmentMappings = mappings;
    this.showResults = true;
    this.showNotification('Department mappings updated');
  }
  
  onCategoryWorkareaMappingsChanged(mappings: CategoryWorkareaMapping[]): void {
    this.categoryMappings = mappings;
    this.showResults = true;
    this.showNotification('Category-workarea mappings updated');
  }
  
  private showNotification(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }
}
