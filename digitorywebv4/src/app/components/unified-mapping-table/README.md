# Unified Mapping Table Component

## Overview
The Unified Mapping Table Component replaces the confusing multi-step stepper interface with a single, comprehensive view for configuring Department → Category → Workarea mappings.

## Problem Solved
**Before**: Users had to navigate through 3 separate steps:
1. Select Departments
2. Map Categories (per department)  
3. Map Workareas (per category)

This made it difficult to:
- See the complete mapping chain
- Understand relationships between departments, categories, and workareas
- Validate the configuration
- Make quick changes

**After**: Single table view showing the complete mapping chain with:
- Real-time preview of relationships
- Visual validation
- Quick setup templates
- Professional UI/UX

## Features

### 🚀 Quick Setup Templates
- **Restaurant Template**: Pre-configured for Food, Bakery, Wood Fire Pizza, Liquor, Beverages
- **Bar Template**: Pre-configured for Liquor, Beverages, Snacks  
- **Bakery Template**: Pre-configured for Bakery, Beverages, Food
- One-click setup with business-appropriate defaults

### 📊 Unified Table View
- Single table showing Department → Category → Workarea chain
- Real-time preview of mapping relationships
- Visual chips for selected categories
- Inline workarea selection for each category

### ✅ Smart Validation
- Real-time validation with clear error messages
- Visual indicators for incomplete configurations
- Summary statistics showing total mappings, categories used, workareas used

### 🎨 Professional UI/UX
- Orange theme consistent with brand
- Responsive design for mobile/tablet
- Loading states and progress indicators
- Success/error notifications

## Usage

### Basic Usage
```typescript
<app-unified-mapping-table
  [tenantId]="user?.tenantId"
  [autoEmit]="true"
  [showAsDialog]="true"
  (mappingsChanged)="onDepartmentMappingsChanged($event)"
  (categoryWorkareaMappingsChanged)="onCategoryWorkareaMappingsChanged($event)"
  (dialogClosed)="closeMappingDialog()">
</app-unified-mapping-table>
```

### Input Properties
- `tenantId: string` - The tenant ID for API calls
- `autoEmit: boolean` - Whether to auto-emit changes (default: true)
- `showAsDialog: boolean` - Whether component is shown in a dialog (default: false)

### Output Events
- `mappingsChanged` - Emits when department-category mappings change
- `categoryWorkareaMappingsChanged` - Emits when category-workarea mappings change
- `dialogClosed` - Emits when user closes the dialog

## Example Configuration

The component supports complex mappings like:

| Department | Categories | Workareas |
|------------|------------|-----------|
| Food | FOOD | MAIN-KITCHEN |
| Bakery | FOOD, BAKERY | BAKERY |
| Wood Fire Pizza | FOOD, BAKERY | BAKERY |
| Liquor | LIQUOR | BAR |
| Beverages | BEVERAGES | BAR |

This allows:
- Multiple departments mapping to same category (Food, Bakery, Wood Fire Pizza → FOOD)
- Single department mapping to multiple categories (Bakery → FOOD + BAKERY)
- Categories mapping to multiple workareas

## Technical Details

### Data Structures
```typescript
interface UnifiedMappingRow {
  departmentId: string;
  departmentName: string;
  selectedCategories: string[];
  categoryWorkareaMappings: { [category: string]: string[] };
}

interface MappingTemplate {
  name: string;
  description: string;
  mappings: {
    [departmentName: string]: {
      categories: string[];
      workareas: { [category: string]: string[] };
    };
  };
}
```

### API Integration
- Reuses existing department, category, and workarea APIs
- Maintains compatibility with existing data structures
- No backend changes required

## Benefits

1. **Reduced Complexity**: From 3 steps to 1 unified view
2. **Visual Clarity**: See complete Department → Category → Workarea chain
3. **Quick Setup**: Templates for common business types
4. **Error Prevention**: Real-time validation with clear messages
5. **Professional Look**: Consistent with orange/white theme
6. **Better UX**: Intuitive interface reduces configuration time and errors

## Files
- `unified-mapping-table.component.ts` - Main component logic
- `unified-mapping-table.component.html` - Template with table view
- `unified-mapping-table.component.scss` - Professional styling
- `README.md` - This documentation

## Integration
The component is integrated into the Smart Dashboard and replaces the previous stepper-based approach. Users will see the new interface when they click "Configure Department & Category Mapping".
