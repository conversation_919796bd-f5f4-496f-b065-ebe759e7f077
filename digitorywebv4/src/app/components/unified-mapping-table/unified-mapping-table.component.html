<!-- Unified Mapping Table Component -->
<div class="unified-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading mapping configuration...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <div class="mapping-header">
      <h3>Department → Category → Workarea Mapping</h3>
      <p class="mapping-description">Configure the complete mapping chain in one view</p>
    </div>

    <!-- Quick Setup Templates -->
    <div class="quick-setup-section" *ngIf="showQuickSetup">
      <h4>
        <mat-icon>flash_on</mat-icon>
        Quick Setup Templates
      </h4>
      <p class="template-description">Choose a template that matches your business type to get started quickly</p>
      <div class="template-buttons">
        <button mat-raised-button color="primary" (click)="applyTemplate('restaurant')">
          <mat-icon>restaurant</mat-icon>
          Restaurant Template
          <span class="template-subtitle">Food, Bakery, Beverages, Liquor</span>
        </button>
        <button mat-raised-button color="primary" (click)="applyTemplate('bar')">
          <mat-icon>local_bar</mat-icon>
          Bar Template
          <span class="template-subtitle">Liquor, Beverages, Snacks</span>
        </button>
        <button mat-raised-button color="primary" (click)="applyTemplate('bakery')">
          <mat-icon>bakery_dining</mat-icon>
          Bakery Template
          <span class="template-subtitle">Bakery, Beverages, Food</span>
        </button>
      </div>
      <div class="template-actions">
        <button mat-button (click)="showQuickSetup = false">
          <mat-icon>close</mat-icon>
          Skip Templates
        </button>
      </div>
    </div>

    <!-- Main Mapping Table -->
    <div class="mapping-table-container">
      <table mat-table [dataSource]="mappingDataSource" class="unified-mapping-table">

        <!-- Department Column -->
        <ng-container matColumnDef="department">
          <th mat-header-cell *matHeaderCellDef>POS Department</th>
          <td mat-cell *matCellDef="let row">
            <div class="department-cell">
              <mat-icon class="dept-icon">store</mat-icon>
              <span class="dept-name">{{row.departmentName}}</span>
              <span class="dept-id">({{row.departmentId}})</span>
            </div>
          </td>
        </ng-container>

        <!-- Categories Column -->
        <ng-container matColumnDef="categories">
          <th mat-header-cell *matHeaderCellDef>Inventory Categories</th>
          <td mat-cell *matCellDef="let row">
            <mat-form-field appearance="outline" class="category-select">
              <mat-select multiple
                         [value]="row.selectedCategories"
                         (selectionChange)="onCategoryChange(row, $event)"
                         placeholder="Select categories">
                <mat-option *ngFor="let category of availableCategories" [value]="category">
                  {{category}}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <!-- Category chips display -->
            <div class="category-chips" *ngIf="row.selectedCategories?.length">
              <mat-chip-listbox>
                <mat-chip-option *ngFor="let category of row.selectedCategories"
                                [removable]="true"
                                (removed)="removeCategory(row, category)">
                  {{category}}
                  <mat-icon matChipRemove>cancel</mat-icon>
                </mat-chip-option>
              </mat-chip-listbox>
            </div>
          </td>
        </ng-container>

        <!-- Workareas Column -->
        <ng-container matColumnDef="workareas">
          <th mat-header-cell *matHeaderCellDef>Mapped Workareas</th>
          <td mat-cell *matCellDef="let row">
            <div class="workarea-mapping" *ngFor="let category of row.selectedCategories">
              <div class="category-workarea-row">
                <span class="category-label">{{category}}:</span>
                <mat-form-field appearance="outline" class="workarea-select">
                  <mat-select multiple
                             [value]="getCategoryWorkareas(category)"
                             (selectionChange)="onWorkareaChange(category, $event)"
                             placeholder="Select workareas">
                    <mat-option *ngFor="let workarea of availableWorkareas" [value]="workarea">
                      {{workarea}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>

            <!-- Show message if no categories selected -->
            <div class="no-categories-message" *ngIf="!row.selectedCategories?.length">
              <mat-icon>info</mat-icon>
              <span>Select categories first</span>
            </div>
          </td>
        </ng-container>

        <!-- Preview Column -->
        <ng-container matColumnDef="preview">
          <th mat-header-cell *matHeaderCellDef>Mapping Preview</th>
          <td mat-cell *matCellDef="let row">
            <div class="mapping-preview">
              <div class="mapping-chain" *ngFor="let category of row.selectedCategories">
                <div class="chain-item dept">{{row.departmentName}}</div>
                <mat-icon class="chain-arrow">arrow_forward</mat-icon>
                <div class="chain-item category">{{category}}</div>
                <mat-icon class="chain-arrow">arrow_forward</mat-icon>
                <div class="chain-item workareas">
                  <span *ngFor="let workarea of getCategoryWorkareas(category); let last = last">
                    {{workarea}}<span *ngIf="!last">, </span>
                  </span>
                </div>
              </div>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <!-- Summary Section -->
    <div class="mapping-summary">
      <h4>Configuration Summary</h4>
      <div class="summary-stats">
        <div class="stat-item">
          <span class="stat-number">{{getTotalMappings()}}</span>
          <span class="stat-label">Total Mappings</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{getUniqueCategoriesCount()}}</span>
          <span class="stat-label">Categories Used</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{getUniqueWorkAreasCount()}}</span>
          <span class="stat-label">Workareas Used</span>
        </div>
      </div>
    </div>

    <!-- Validation Messages -->
    <div class="validation-messages" *ngIf="validationErrors.length">
      <mat-card class="error-card">
        <mat-card-header>
          <mat-icon class="error-icon">warning</mat-icon>
          <mat-card-title>Configuration Issues</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ul>
            <li *ngFor="let error of validationErrors" class="error-item">{{error}}</li>
          </ul>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-button (click)="resetConfiguration()">Reset</button>
      <button mat-button (click)="previewConfiguration()">Preview</button>
      <button mat-raised-button color="primary"
              [disabled]="!isConfigurationValid() || isSaving"
              (click)="saveConfiguration()">
        <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
        <span *ngIf="!isSaving">Save Configuration</span>
        <span *ngIf="isSaving">Saving...</span>
      </button>
    </div>
  </div>
</div>
