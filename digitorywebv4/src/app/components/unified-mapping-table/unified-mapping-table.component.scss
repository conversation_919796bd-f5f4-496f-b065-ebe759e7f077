.unified-mapping-container {
  padding: 20px;
  max-width: 100%;
  
  &.dialog-mode {
    padding: 16px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    
    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .mapping-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .mapping-header {
    text-align: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0 0 8px 0;
      color: #333;
      font-weight: 500;
    }
    
    .mapping-description {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .quick-setup-section {
    background: linear-gradient(135deg, #fff8f5 0%, #fff3e8 100%);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #ffe0cc;
    margin-bottom: 24px;

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #ff6b35;
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }

    .template-description {
      margin: 0 0 16px 0;
      color: #666;
      font-size: 14px;
    }

    .template-buttons {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      margin-bottom: 16px;

      button {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 16px 20px;
        min-width: 160px;
        height: auto;

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }

        .template-subtitle {
          font-size: 11px;
          opacity: 0.8;
          text-align: center;
          line-height: 1.2;
        }
      }
    }

    .template-actions {
      display: flex;
      justify-content: center;

      button {
        color: #666;
        font-size: 12px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .mapping-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 16px;

    .unified-mapping-table {
      width: 100%;

      .mat-mdc-header-cell {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        color: white;
        font-weight: 600;
        border-bottom: 2px solid #e0e0e0;
        text-align: center;
        font-size: 14px;
      }

      .mat-mdc-cell {
        padding: 16px 12px;
        border-bottom: 1px solid #f0f0f0;
        vertical-align: top;
      }

      .mat-mdc-row:hover {
        background: #fafafa;
        transition: background-color 0.2s ease;
      }

      .mat-mdc-row:nth-child(even) {
        background: #fafbfc;
      }
    }
  }

  .department-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .dept-icon {
      color: #ff6b35;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
    
    .dept-name {
      font-weight: 500;
      color: #333;
    }
    
    .dept-id {
      color: #666;
      font-size: 12px;
    }
  }

  .category-select {
    width: 100%;
    margin-bottom: 8px;
    
    .mat-mdc-form-field-wrapper {
      padding-bottom: 0;
    }
  }

  .category-chips {
    .mat-mdc-chip-listbox {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      
      .mat-mdc-chip-option {
        font-size: 12px;
        min-height: 24px;
        
        .mat-mdc-chip-remove {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .workarea-mapping {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .category-workarea-row {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .category-label {
        font-weight: 500;
        color: #333;
        min-width: 80px;
        font-size: 12px;
      }
      
      .workarea-select {
        flex: 1;
        
        .mat-mdc-form-field-wrapper {
          padding-bottom: 0;
        }
      }
    }
  }

  .no-categories-message {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-style: italic;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  .mapping-preview {
    .mapping-chain {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
      font-size: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .chain-item {
        padding: 4px 8px;
        border-radius: 4px;
        
        &.dept {
          background: #e3f2fd;
          color: #1976d2;
          font-weight: 500;
        }
        
        &.category {
          background: #fff3e0;
          color: #f57c00;
          font-weight: 500;
        }
        
        &.workareas {
          background: #e8f5e8;
          color: #388e3c;
        }
      }
      
      .chain-arrow {
        color: #666;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }

  .mapping-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
    
    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }
    
    .summary-stats {
      display: flex;
      gap: 24px;
      
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .stat-number {
          font-size: 24px;
          font-weight: 600;
          color: #ff6b35;
        }
        
        .stat-label {
          font-size: 12px;
          color: #666;
          text-align: center;
        }
      }
    }
  }

  .validation-messages {
    .error-card {
      border-left: 4px solid #f44336;
      
      .mat-mdc-card-header {
        padding-bottom: 8px;
        
        .error-icon {
          color: #f44336;
          margin-right: 8px;
        }
        
        .mat-mdc-card-title {
          color: #f44336;
          font-size: 16px;
          font-weight: 500;
        }
      }
      
      .mat-mdc-card-content {
        ul {
          margin: 0;
          padding-left: 20px;
          
          .error-item {
            color: #d32f2f;
            margin-bottom: 4px;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
    
    button {
      min-width: 120px;
      
      &[color="primary"] {
        display: flex;
        align-items: center;
        gap: 8px;
        
        mat-spinner {
          margin-right: 8px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .unified-mapping-container {
    padding: 12px;
    
    .mapping-table-container {
      overflow-x: auto;
      
      .unified-mapping-table {
        min-width: 800px;
      }
    }
    
    .template-buttons {
      flex-direction: column;
      
      button {
        width: 100%;
      }
    }
    
    .summary-stats {
      flex-direction: column;
      gap: 16px;
    }
    
    .action-buttons {
      flex-direction: column;
      
      button {
        width: 100%;
      }
    }
  }
}
