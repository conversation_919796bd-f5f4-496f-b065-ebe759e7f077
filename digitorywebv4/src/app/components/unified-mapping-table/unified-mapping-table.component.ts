import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, FormControl } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { Subject, takeUntil } from 'rxjs';

import { DepartmentService } from '../../services/department.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

export interface Department {
  id: string;
  name: string;
  code?: string;
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
}

export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
  virtualWorkAreas?: any[];
}

export interface WorkAreaData {
  restaurantIdOld: string;
  branchName: string;
  workAreas: string[];
  disabled: boolean;
}

export interface UnifiedMappingRow {
  departmentId: string;
  departmentName: string;
  selectedCategories: string[];
  categoryWorkareaMappings: { [category: string]: string[] };
}

export interface MappingTemplate {
  name: string;
  description: string;
  mappings: {
    [departmentName: string]: {
      categories: string[];
      workareas: { [category: string]: string[] };
    };
  };
}

@Component({
  selector: 'app-unified-mapping-table',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTableModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './unified-mapping-table.component.html',
  styleUrls: ['./unified-mapping-table.component.scss']
})
export class UnifiedMappingTableComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Inputs
  @Input() tenantId: string = '';
  @Input() autoEmit: boolean = true;
  @Input() showAsDialog: boolean = false;

  // Outputs
  @Output() mappingsChanged = new EventEmitter<DepartmentCategoryMapping[]>();
  @Output() categoryWorkareaMappingsChanged = new EventEmitter<CategoryWorkareaMapping[]>();
  @Output() dialogClosed = new EventEmitter<void>();

  // Data
  departments: Department[] = [];
  categories: string[] = [];
  workAreas: WorkAreaData[] = [];
  allWorkAreas: string[] = [];
  
  // Table data
  mappingDataSource: UnifiedMappingRow[] = [];
  displayedColumns: string[] = ['department', 'categories', 'workareas', 'preview'];
  
  // Available options
  availableCategories: string[] = [];
  availableWorkareas: string[] = [];
  
  // Category-workarea mappings
  categoryWorkareaMap: { [category: string]: string[] } = {};
  
  // State
  isLoading = true;
  isSaving = false;
  showQuickSetup = true;
  validationErrors: string[] = [];

  // Templates
  mappingTemplates: MappingTemplate[] = [
    {
      name: 'restaurant',
      description: 'Standard Restaurant Setup',
      mappings: {
        'Food': { categories: ['FOOD'], workareas: { 'FOOD': ['MAIN-KITCHEN'] } },
        'Bakery': { categories: ['FOOD', 'BAKERY'], workareas: { 'FOOD': ['BAKERY'], 'BAKERY': ['BAKERY'] } },
        'Wood Fire Pizza': { categories: ['FOOD', 'BAKERY'], workareas: { 'FOOD': ['BAKERY'], 'BAKERY': ['BAKERY'] } },
        'Liquor': { categories: ['LIQUOR'], workareas: { 'LIQUOR': ['BAR'] } },
        'Beverages': { categories: ['BEVERAGES'], workareas: { 'BEVERAGES': ['BAR'] } }
      }
    },
    {
      name: 'bar',
      description: 'Bar & Lounge Setup',
      mappings: {
        'Liquor': { categories: ['LIQUOR'], workareas: { 'LIQUOR': ['BAR'] } },
        'Beverages': { categories: ['BEVERAGES'], workareas: { 'BEVERAGES': ['BAR'] } },
        'Snacks': { categories: ['FOOD'], workareas: { 'FOOD': ['BAR'] } }
      }
    },
    {
      name: 'bakery',
      description: 'Bakery & Cafe Setup',
      mappings: {
        'Bakery': { categories: ['BAKERY'], workareas: { 'BAKERY': ['BAKERY'] } },
        'Beverages': { categories: ['BEVERAGES'], workareas: { 'BEVERAGES': ['COUNTER'] } },
        'Food': { categories: ['FOOD'], workareas: { 'FOOD': ['BAKERY'] } }
      }
    }
  ];

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (!this.tenantId) {
      const user = this.authService.getCurrentUser();
      this.tenantId = user?.tenantId || '';
    }

    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadData(): void {
    Promise.all([
      this.loadDepartments(),
      this.loadCategories(),
      this.loadWorkAreas(),
      this.loadExistingMappings(),
      this.loadCategoryWorkareaMappings()
    ]).then(() => {
      this.extractAllWorkAreas();
      this.initializeMappingTable();
      this.isLoading = false;
      this.cdr.detectChanges();
    }).catch(() => {
      this.isLoading = false;
      this.cdr.detectChanges();
    });
  }

  private loadDepartments(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartments(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (departments) => {
            this.departments = departments;
            resolve();
          },
          error: (error) => {
            this.departments = [];
            reject(error);
          }
        });
    });
  }

  private loadCategories(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.smartDashboardService.getCategories(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (categories) => {
            this.categories = categories;
            this.availableCategories = [...categories];
            resolve();
          },
          error: (error) => {
            this.categories = [];
            this.availableCategories = [];
            reject(error);
          }
        });
    });
  }

  private loadWorkAreas(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.smartDashboardService.getWorkAreas(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (workAreas) => {
            this.workAreas = workAreas;
            resolve();
          },
          error: (error) => {
            this.workAreas = [];
            reject(error);
          }
        });
    });
  }

  private loadExistingMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartmentCategoryMappings(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            // Store existing mappings for initialization
            this.existingDepartmentMappings = mappings;
            resolve();
          },
          error: (error) => {
            this.existingDepartmentMappings = [];
            reject(error);
          }
        });
    });
  }

  private loadCategoryWorkareaMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getCategoryWorkareaMappings(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            // Convert to our internal format
            mappings.forEach(mapping => {
              this.categoryWorkareaMap[mapping.categoryName] = mapping.workAreas;
            });
            resolve();
          },
          error: (error) => {
            this.categoryWorkareaMap = {};
            reject(error);
          }
        });
    });
  }

  private extractAllWorkAreas(): void {
    const workAreaSet = new Set<string>();
    this.workAreas.forEach(wa => {
      wa.workAreas.forEach(area => workAreaSet.add(area));
    });
    this.allWorkAreas = Array.from(workAreaSet);
    this.availableWorkareas = [...this.allWorkAreas];
  }

  private existingDepartmentMappings: DepartmentCategoryMapping[] = [];

  private initializeMappingTable(): void {
    this.mappingDataSource = this.departments.map(dept => {
      // Find existing mapping for this department
      const existingMapping = this.existingDepartmentMappings.find(m => m.departmentId === dept.id);

      const row: UnifiedMappingRow = {
        departmentId: dept.id,
        departmentName: dept.name,
        selectedCategories: existingMapping ? existingMapping.categories : [],
        categoryWorkareaMappings: {}
      };

      // Initialize category-workarea mappings for selected categories
      if (existingMapping) {
        existingMapping.categories.forEach(category => {
          row.categoryWorkareaMappings[category] = this.categoryWorkareaMap[category] || [];
        });
      }

      return row;
    });
  }

  // Template methods
  applyTemplate(templateName: string): void {
    const template = this.mappingTemplates.find(t => t.name === templateName);
    if (!template) return;

    // Apply template to matching departments
    this.mappingDataSource.forEach(row => {
      const templateMapping = template.mappings[row.departmentName];
      if (templateMapping) {
        row.selectedCategories = [...templateMapping.categories];
        row.categoryWorkareaMappings = { ...templateMapping.workareas };

        // Update global category-workarea map
        Object.keys(templateMapping.workareas).forEach(category => {
          this.categoryWorkareaMap[category] = templateMapping.workareas[category];
        });
      }
    });

    this.validateConfiguration();
    this.emitChanges();
    this.cdr.detectChanges();
  }

  // Category selection methods
  onCategoryChange(row: UnifiedMappingRow, event: any): void {
    const selectedCategories = event.value;
    row.selectedCategories = selectedCategories;

    // Initialize workarea mappings for new categories
    selectedCategories.forEach((category: string) => {
      if (!row.categoryWorkareaMappings[category]) {
        row.categoryWorkareaMappings[category] = this.categoryWorkareaMap[category] || [];
      }
    });

    // Remove workarea mappings for deselected categories
    Object.keys(row.categoryWorkareaMappings).forEach(category => {
      if (!selectedCategories.includes(category)) {
        delete row.categoryWorkareaMappings[category];
      }
    });

    this.validateConfiguration();
    this.emitChanges();
  }

  removeCategory(row: UnifiedMappingRow, category: string): void {
    row.selectedCategories = row.selectedCategories.filter(c => c !== category);
    delete row.categoryWorkareaMappings[category];
    this.validateConfiguration();
    this.emitChanges();
  }

  // Workarea selection methods
  getCategoryWorkareas(category: string): string[] {
    return this.categoryWorkareaMap[category] || [];
  }

  onWorkareaChange(category: string, event: any): void {
    const selectedWorkareas = event.value;
    this.categoryWorkareaMap[category] = selectedWorkareas;

    // Update all rows that have this category
    this.mappingDataSource.forEach(row => {
      if (row.selectedCategories.includes(category)) {
        row.categoryWorkareaMappings[category] = selectedWorkareas;
      }
    });

    this.validateConfiguration();
    this.emitChanges();
  }

  // Validation methods
  private validateConfiguration(): void {
    this.validationErrors = [];

    // Check for departments without categories
    const departmentsWithoutCategories = this.mappingDataSource.filter(row =>
      row.selectedCategories.length === 0
    );
    if (departmentsWithoutCategories.length > 0) {
      this.validationErrors.push(
        `Departments without categories: ${departmentsWithoutCategories.map(r => r.departmentName).join(', ')}`
      );
    }

    // Check for categories without workareas
    const categoriesWithoutWorkareas: string[] = [];
    Object.keys(this.categoryWorkareaMap).forEach(category => {
      if (!this.categoryWorkareaMap[category] || this.categoryWorkareaMap[category].length === 0) {
        categoriesWithoutWorkareas.push(category);
      }
    });
    if (categoriesWithoutWorkareas.length > 0) {
      this.validationErrors.push(
        `Categories without workareas: ${categoriesWithoutWorkareas.join(', ')}`
      );
    }
  }

  isConfigurationValid(): boolean {
    return this.validationErrors.length === 0;
  }

  // Summary methods
  getTotalMappings(): number {
    return this.mappingDataSource.reduce((total, row) =>
      total + row.selectedCategories.length, 0
    );
  }

  getUniqueCategoriesCount(): number {
    const categories = new Set<string>();
    this.mappingDataSource.forEach(row => {
      row.selectedCategories.forEach(cat => categories.add(cat));
    });
    return categories.size;
  }

  getUniqueWorkAreasCount(): number {
    const workareas = new Set<string>();
    Object.values(this.categoryWorkareaMap).forEach(areas => {
      areas.forEach(area => workareas.add(area));
    });
    return workareas.size;
  }

  // Action methods
  onCancel(): void {
    this.dialogClosed.emit();
  }

  resetConfiguration(): void {
    this.mappingDataSource.forEach(row => {
      row.selectedCategories = [];
      row.categoryWorkareaMappings = {};
    });
    this.categoryWorkareaMap = {};
    this.validationErrors = [];
    this.emitChanges();
    this.cdr.detectChanges();
  }

  previewConfiguration(): void {
    console.log('Current Configuration:', {
      departmentMappings: this.getCurrentDepartmentMappings(),
      categoryWorkareaMappings: this.getCurrentCategoryWorkareaMappings()
    });
  }

  saveConfiguration(): void {
    if (!this.isConfigurationValid()) {
      this.showError('Please fix validation errors before saving.');
      return;
    }

    this.isSaving = true;
    const departmentMappings = this.getCurrentDepartmentMappings();
    const categoryWorkareaMappings = this.getCurrentCategoryWorkareaMappings();

    // Save department-category mappings first
    this.departmentService.saveDepartmentCategoryMappings(this.tenantId, departmentMappings)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            // Save category-workarea mappings
            this.departmentService.saveCategoryWorkareaMappings(this.tenantId, categoryWorkareaMappings)
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (success) => {
                  this.isSaving = false;
                  if (success) {
                    this.showSuccess('Configuration saved successfully!');
                    this.dialogClosed.emit();
                  } else {
                    this.showError('Failed to save category-workarea mappings.');
                  }
                },
                error: () => {
                  this.isSaving = false;
                  this.showError('Error saving category-workarea mappings.');
                }
              });
          } else {
            this.isSaving = false;
            this.showError('Failed to save department-category mappings.');
          }
        },
        error: () => {
          this.isSaving = false;
          this.showError('Error saving department-category mappings.');
        }
      });
  }

  // Helper methods for data conversion
  private getCurrentDepartmentMappings(): DepartmentCategoryMapping[] {
    return this.mappingDataSource
      .filter(row => row.selectedCategories.length > 0)
      .map(row => ({
        departmentId: row.departmentId,
        departmentName: row.departmentName,
        categories: row.selectedCategories
      }));
  }

  private getCurrentCategoryWorkareaMappings(): CategoryWorkareaMapping[] {
    const categoryMappings: CategoryWorkareaMapping[] = [];

    Object.keys(this.categoryWorkareaMap).forEach(category => {
      if (this.categoryWorkareaMap[category].length > 0) {
        categoryMappings.push({
          categoryName: category,
          workAreas: this.categoryWorkareaMap[category],
          virtualWorkAreas: []
        });
      }
    });

    return categoryMappings;
  }

  private emitChanges(): void {
    if (this.autoEmit) {
      this.mappingsChanged.emit(this.getCurrentDepartmentMappings());
      this.categoryWorkareaMappingsChanged.emit(this.getCurrentCategoryWorkareaMappings());
    }
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
