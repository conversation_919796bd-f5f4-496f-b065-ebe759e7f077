<!-- Visual Mapping Flow Component -->
<div class="visual-mapping-container">
  <div class="mapping-header">
    <h3>Visual Department Mapping</h3>
    <p>Drag and connect departments to categories to workareas</p>
  </div>

  <div class="mapping-flow">
    <!-- Departments Column -->
    <div class="mapping-column departments-column">
      <h4>POS Departments</h4>
      <div class="items-container">
        <div class="mapping-item department-item" 
             *ngFor="let dept of departments"
             [class.selected]="selectedDepartment?.id === dept.id"
             (click)="selectDepartment(dept)">
          <mat-icon class="item-icon">store</mat-icon>
          <div class="item-content">
            <span class="item-name">{{dept.name}}</span>
            <span class="item-id">ID: {{dept.id}}</span>
          </div>
          <div class="connection-dots">
            <div class="dot" *ngFor="let connection of dept.connections"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Connection Lines -->
    <div class="connection-lines">
      <svg class="connection-svg" #connectionSvg>
        <path *ngFor="let connection of connections" 
              [attr.d]="connection.path"
              class="connection-path"
              [class.active]="connection.active"></path>
      </svg>
    </div>

    <!-- Categories Column -->
    <div class="mapping-column categories-column">
      <h4>Inventory Categories</h4>
      <div class="items-container">
        <div class="mapping-item category-item"
             *ngFor="let category of categories"
             [class.connected]="isCategoryConnected(category)"
             [class.selected]="selectedCategory === category"
             (click)="selectCategory(category)">
          <mat-icon class="item-icon">category</mat-icon>
          <div class="item-content">
            <span class="item-name">{{category}}</span>
            <div class="connected-departments" *ngIf="getCategoryDepartments(category).length">
              <mat-chip-listbox>
                <mat-chip-option *ngFor="let dept of getCategoryDepartments(category)">
                  {{dept}}
                </mat-chip-option>
              </mat-chip-listbox>
            </div>
          </div>
          <div class="connection-dots">
            <div class="dot" *ngFor="let connection of getCategoryConnections(category)"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Workareas Column -->
    <div class="mapping-column workareas-column">
      <h4>Work Areas</h4>
      <div class="items-container">
        <div class="mapping-item workarea-item"
             *ngFor="let workarea of workareas"
             [class.connected]="isWorkareaConnected(workarea)"
             [class.available]="isWorkareaAvailable(workarea)"
             (click)="selectWorkarea(workarea)">
          <mat-icon class="item-icon">kitchen</mat-icon>
          <div class="item-content">
            <span class="item-name">{{workarea}}</span>
            <div class="connected-categories" *ngIf="getWorkareaCategories(workarea).length">
              <mat-chip-listbox>
                <mat-chip-option *ngFor="let cat of getWorkareaCategories(workarea)">
                  {{cat}}
                </mat-chip-option>
              </mat-chip-listbox>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Configuration Panel -->
  <div class="configuration-panel" *ngIf="selectedDepartment">
    <mat-card>
      <mat-card-header>
        <mat-card-title>Configure: {{selectedDepartment.name}}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="config-section">
          <h5>Select Categories</h5>
          <mat-selection-list [(ngModel)]="selectedDepartment.categories">
            <mat-list-option *ngFor="let category of availableCategories" [value]="category">
              {{category}}
            </mat-list-option>
          </mat-selection-list>
        </div>

        <div class="config-section" *ngIf="selectedDepartment.categories?.length">
          <h5>Configure Workareas for Each Category</h5>
          <div class="category-workarea-config" *ngFor="let category of selectedDepartment.categories">
            <h6>{{category}} → Workareas</h6>
            <mat-selection-list [(ngModel)]="categoryWorkareaMap[category]">
              <mat-list-option *ngFor="let workarea of getAvailableWorkAreasForCategory(category)" 
                              [value]="workarea">
                {{workarea}}
              </mat-list-option>
            </mat-selection-list>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button (click)="clearDepartmentConfig()">Clear</button>
        <button mat-raised-button color="primary" (click)="saveDepartmentConfig()">Apply</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Live Preview -->
  <div class="live-preview">
    <h4>Current Configuration</h4>
    <div class="preview-table">
      <table mat-table [dataSource]="previewData">
        <ng-container matColumnDef="department">
          <th mat-header-cell *matHeaderCellDef>Department</th>
          <td mat-cell *matCellDef="let element">{{element.department}}</td>
        </ng-container>
        <ng-container matColumnDef="categories">
          <th mat-header-cell *matHeaderCellDef>Categories</th>
          <td mat-cell *matCellDef="let element">{{element.categories.join(', ')}}</td>
        </ng-container>
        <ng-container matColumnDef="workareas">
          <th mat-header-cell *matHeaderCellDef>Workareas</th>
          <td mat-cell *matCellDef="let element">{{element.workareas.join(', ')}}</td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="previewColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: previewColumns;"></tr>
      </table>
    </div>
  </div>
</div>
