    .dialog-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding: 10px;
      border-top: 1px solid #e0e0e0;
      background: #fafafa;

      .cancel-btn {
        color: #666;

        &:hover {
          background: #f0f0f0;
        }
      }

      .save-btn {
        background: #ff6b35;
        color: white;

        &:hover {
          background: #ff5722;
        }
      }
    }

.unified-department-mapping {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  // When shown in dialog, remove background and shadow
  &.dialog-mode {
    background: transparent;
    box-shadow: none;
    border-radius: 0;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;

    mat-spinner {
      margin-bottom: 16px;
    }

    p {
      color: #666;
      margin: 0;
    }
  }

  .mapping-content {
    .section-header {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;

      > div:first-child {
        display: flex;
        align-items: center;
      }

      mat-icon {
        margin-right: 8px;
        color: #ff6b35;
      }

      h4 {
        margin: 0;
        color: #333;
        font-weight: 500;
      }

      .section-description {
        display: flex;
        align-items: center;
        margin-top: 8px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 13px;
        color: #666;

        .info-icon {
          font-size: 16px;
          margin-right: 6px;
          color: #2196f3;
        }
      }
    }

    .departments-section {
      margin-bottom: 24px;

      .department-filter-field {
        width: 100%;
        max-width: 600px;

        ::ng-deep {
          .mat-mdc-form-field-wrapper {
            height: 40px;
            min-height: 40px;
          }

          .mat-mdc-form-field-infix {
            padding: 8px 12px;
            min-height: 24px;
          }

          .mat-mdc-form-field-subscript-wrapper {
            display: none;
          }
        }
      }

      .select-all-custom-option {
        padding: 12px 16px;
        cursor: pointer;
        color: #ff6b35;
        font-weight: 500;
        border-bottom: 1px solid #eee;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }

    .department-mapping-section {
      margin-bottom: 24px;

      .mappings-container {
        .mapping-row {
          display: flex;
          align-items: center;
          gap: 20px;
          padding: 16px;
          margin-bottom: 12px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background: #fafafa;

          .department-info {
            display: flex;
            align-items: center;
            min-width: 200px;
            flex-shrink: 0;

            .department-icon {
              margin-right: 12px;
              color: #ff6b35;
            }

            .department-details {
              h5 {
                margin: 0 0 4px 0;
                font-weight: 500;
                color: #333;
              }

              .department-id {
                font-size: 12px;
                color: #666;
              }
            }
          }

          .category-selection {
            flex: 1;

            .categories-field {
              width: 100%;

              ::ng-deep {
                .mat-mdc-form-field-wrapper {
                  height: 40px;
                  min-height: 40px;
                }

                .mat-mdc-form-field-infix {
                  padding: 8px 12px;
                  min-height: 24px;
                }

                .mat-mdc-form-field-subscript-wrapper {
                  display: none;
                }
              }
            }
          }
        }
      }
    }

    .category-workarea-section {
      margin-bottom: 24px;

      .workarea-mapping-container {
        .selected-categories-info {
          padding: 12px 16px;
          background: #f0f8ff;
          border: 1px solid #e3f2fd;
          border-radius: 6px;
          margin-bottom: 16px;

          p {
            margin: 0;
            color: #1976d2;
            font-size: 14px;

            strong {
              color: #0d47a1;
            }
          }
        }

        .category-workarea-mapping-wrapper {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          background: #fafafa;
        }
      }
    }

    .save-actions {
      margin-bottom: 24px;

      .save-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;

        .save-info {
          display: flex;
          align-items: center;
          gap: 12px;

          mat-icon {
            color: #ff6b35;
            font-size: 24px;
            width: 24px;
            height: 24px;
          }

          .save-text {
            h4 {
              margin: 0 0 4px 0;
              color: #333;
              font-weight: 500;
              font-size: 16px;
            }

            p {
              margin: 0;
              color: #666;
              font-size: 14px;
            }
          }
        }

        .save-button {
          min-width: 200px;
          height: 40px;

          .button-spinner {
            margin-right: 8px;
          }

          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }

    .summary-section {
      margin-bottom: 24px;

      .summary-cards {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;

        .summary-card {
          flex: 1;
          min-width: 150px;
          padding: 16px;
          background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
          color: white;
          border-radius: 8px;
          text-align: center;

          .summary-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .summary-label {
            font-size: 12px;
            opacity: 0.9;
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      color: #666;

      .empty-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #ccc;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        color: #333;
      }

      p {
        margin: 0;
        max-width: 400px;
        margin: 0 auto;
      }
    }

    .no-data-message {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #666;
      background: #f9f9f9;
      border-radius: 8px;
      border: 1px dashed #ddd;

      mat-icon {
        margin-right: 8px;
        color: #999;
      }

      p {
        margin: 0;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .unified-department-mapping {
    padding: 16px;

    .mapping-content {
      .department-mapping-section {
        .mappings-container {
          .mapping-row {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;

            .department-info {
              min-width: auto;
            }
          }
        }
      }

      .summary-section {
        .summary-cards {
          flex-direction: column;

          .summary-card {
            min-width: auto;
          }
        }
      }
    }
  }
}

// Material Design overrides
::ng-deep {
  .mat-mdc-select-panel {
    max-height: 400px;
  }

  .mat-mdc-option {
    &.mat-mdc-option-multiple {
      .mat-pseudo-checkbox {
        margin-right: 8px;
      }
    }
  }

  .mat-mdc-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 4px;
    }
  }

  // ===== CATEGORY-WORKAREA MAPPING STYLES =====

  .category-workarea-section {
    margin-top: 24px;

    .workarea-mapping-container {
      .selected-categories-info {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 16px;
        border-left: 3px solid #ff6b35;

        p {
          margin: 0;
          font-size: 14px;
          color: #333;
        }
      }

      .smart-mapping-info {
        margin-bottom: 16px;

        .info-card {
          display: flex;
          align-items: flex-start;
          gap: 10px;
          padding: 12px;
          background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
          border-radius: 6px;
          border: 1px solid #c8e6c9;
          border-left: 3px solid #4caf50;

          .info-icon {
            font-size: 20px;
            color: #4caf50;
            margin-top: 1px;
            flex-shrink: 0;
          }

          .info-content {
            flex: 1;

            h4 {
              margin: 0 0 2px 0;
              font-size: 14px;
              font-weight: 600;
              color: #2e7d32;
            }

            p {
              margin: 0;
              font-size: 12px;
              color: #388e3c;
              line-height: 1.3;
            }
          }
        }
      }

      .validation-errors {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
        background: #ffebee;
        border-radius: 6px;
        border-left: 3px solid #f44336;
        margin-bottom: 16px;

        .error-icon {
          color: #f44336;
          font-size: 20px;
          flex-shrink: 0;
        }

        .error-list {
          flex: 1;

          .error-message {
            margin: 0 0 4px 0;
            font-size: 13px;
            color: #c62828;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .mapping-form {
        .mappings-container {
          .empty-state-message {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 40px 20px;
            text-align: center;
            color: #666;
            background: #f8f9fa;
            border-radius: 6px;
            border: 2px dashed #dee2e6;

            .empty-icon {
              font-size: 24px;
              color: #adb5bd;
            }

            p {
              margin: 0;
              font-size: 14px;
            }
          }

          .mapping-row {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px;
            margin-bottom: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;

            .category-name {
              display: flex;
              align-items: center;
              gap: 8px;
              min-width: 200px;
              font-weight: 500;
              color: #333;

              .category-icon {
                color: #ff6b35;
                font-size: 18px;
              }
            }

            .workareas-dropdown {
              flex: 1;

              .workareas-field {
                width: 100%;

                ::ng-deep {
                  .mat-mdc-form-field-wrapper {
                    height: 40px;
                    min-height: 40px;
                  }

                  .mat-mdc-form-field-infix {
                    padding: 8px 12px;
                    min-height: 24px;
                  }

                  .mat-mdc-form-field-subscript-wrapper {
                    display: none;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
